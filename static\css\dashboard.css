/* Greek Terminal - Enhanced Modern Styles */

/* Dark Theme with Green/Red Chart Colors */
:root {
    /* Primary Background Colors */
    --bg-primary: #000000;
    --bg-secondary: #1a1a1a;
    --bg-tertiary: #333333;
    --bg-quaternary: #4d4d4d;
    --bg-chart: #121212;
    --bg-chart-alt: #1e1e1e;

    /* Glass Morphism Effects */
    --glass-bg: rgba(255, 255, 255, 0.08);
    --glass-border: rgba(255, 255, 255, 0.15);
    --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);

    /* Text Colors */
    --text-primary: #ffffff;
    --text-secondary: #e0e0e0;
    --text-tertiary: #b0b0b0;
    --text-muted: #808080;
    --text-axis: #999999;

    /* Grayscale Accent Colors */
    --accent-primary: #ffffff;
    --accent-primary-hover: #e0e0e0;
    --accent-success: #ffffff;
    --accent-success-hover: #e0e0e0;
    --accent-danger: #666666;
    --accent-danger-hover: #4d4d4d;
    --accent-warning: #cccccc;
    --accent-warning-hover: #b0b0b0;
    --accent-info: #ffffff;
    --accent-info-hover: #e0e0e0;

    /* Border and Divider Colors */
    --border-color: rgba(255, 255, 255, 0.15);
    --border-hover: rgba(255, 255, 255, 0.25);
    --divider-color: rgba(255, 255, 255, 0.08);
    --grid-color: #666666;

    /* Green and Red Options Colors */
    --call-color: #00FF00;
    --call-color-alt: #00CC00;
    --put-color: #FF0000;
    --put-color-alt: #CC0000;
    --atm-color: #cccccc;
    --header-color: #333333;

    /* Zone Fill Transparency Levels */
    --zone-alpha-1: 0.4;
    --zone-alpha-2: 0.3;
    --zone-alpha-3: 0.2;

    /* Black and White Tab Group Colors */
    --tab-candlestick: linear-gradient(135deg, #333333, #666666);
    --tab-candlestick-border: #666666;
    --tab-gamma: linear-gradient(135deg, #4d4d4d, #808080);
    --tab-gamma-border: #808080;
    --tab-basic: linear-gradient(135deg, #666666, #999999);
    --tab-basic-border: #999999;
    --tab-advanced: linear-gradient(135deg, #1a1a1a, #333333);
    --tab-advanced-border: #333333;
    --tab-oi: linear-gradient(135deg, #808080, #b0b0b0);
    --tab-oi-border: #b0b0b0;
    --tab-gex: linear-gradient(135deg, #333333, #4d4d4d);
    --tab-gex-border: #4d4d4d;

    /* Animation Variables */
    --transition-fast: 0.15s ease-out;
    --transition-normal: 0.3s ease-out;
    --transition-slow: 0.5s ease-out;

    /* Shadow Variables */
    --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.3);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.4);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.5);
    --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.6);
}

/* Enhanced Global Styles */
body {
    background: linear-gradient(135deg, var(--bg-primary) 0%, #1a1a1a 50%, var(--bg-primary) 100%) !important;
    color: var(--text-primary) !important;
    font-family: 'Inter', 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
    line-height: 1.6;
    overflow-x: hidden;
}

.container-fluid {
    max-width: 1600px;
    padding: 0 20px;
}

/* Enhanced Card Styling with Glassmorphism */
.card {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: 16px;
    box-shadow: var(--glass-shadow);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
}

.card:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
    border-color: var(--border-hover);
}

.card-header {
    background: rgba(255, 255, 255, 0.03);
    border-bottom: 1px solid var(--divider-color);
    border-radius: 16px 16px 0 0 !important;
    padding: 1.5rem;
    position: relative;
}

.card-body {
    background: transparent;
    border-radius: 0 0 16px 16px;
    padding: 1.5rem;
}

/* Enhanced Navigation Tabs */
.nav-tabs {
    border-bottom: 2px solid var(--divider-color);
    margin-bottom: 0;
}

.nav-tabs .nav-link {
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    border-bottom: none;
    color: var(--text-secondary);
    margin-right: 4px;
    border-radius: 12px 12px 0 0;
    padding: 12px 20px;
    font-weight: 600;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
    box-shadow: none;
}

.nav-tabs .nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--accent-primary), var(--accent-info));
    transform: scaleX(0);
    transition: transform var(--transition-normal);
}

.nav-tabs .nav-link:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
    transform: translateY(-2px);
    border-color: var(--border-hover);
    box-shadow: none;
}

.nav-tabs .nav-link:hover::before {
    transform: scaleX(1);
}

.nav-tabs .nav-link.active {
    background: var(--glass-bg);
    border-color: var(--accent-primary);
    color: var(--text-primary);
    border-bottom: 2px solid var(--accent-primary);
    box-shadow: none;
}

.nav-tabs .nav-link.active::before {
    transform: scaleX(1);
}

/* Enhanced Form Controls */
.form-control, .form-select {
    background: var(--glass-bg);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    border: 1px solid var(--glass-border);
    border-radius: 14px;
    color: var(--text-primary) !important;
    padding: 14px 18px;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all var(--transition-normal);
    position: relative;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.form-control:focus, .form-select:focus {
    background: rgba(255, 255, 255, 0.1);
    border-color: var(--accent-primary);
    color: var(--text-primary) !important;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2), 0 8px 25px rgba(0, 0, 0, 0.25);
    transform: translateY(-2px);
    outline: none;
}

.form-control:hover, .form-select:hover {
    background: rgba(255, 255, 255, 0.07);
    border-color: var(--border-hover);
    transform: translateY(-1px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

/* Enhanced Select Dropdown Styling */
.form-select {
    cursor: pointer;
}

.form-control::placeholder {
    color: var(--text-muted) !important;
    font-style: italic;
}

/* Enhanced Dropdown Options Styling */
.form-select option {
    background-color: var(--bg-secondary) !important;
    color: var(--text-primary) !important;
    padding: 12px 16px !important;
    border: none !important;
    font-weight: 500;
}

.form-select option:hover {
    background-color: var(--bg-tertiary) !important;
    color: var(--text-primary) !important;
}

.form-select option:checked,
.form-select option:selected {
    background-color: var(--accent-primary) !important;
    color: #000000 !important;
    font-weight: 600;
}

/* Force dropdown background styling */
.form-select {
    background-color: var(--glass-bg) !important;
    color: var(--text-primary) !important;
}

/* Clean dropdown styling without arrows */
.form-select {
    background-image: none !important;
}

/* Small Form Select Variants */
.form-select-sm {
    padding: 10px 14px;
    font-size: 0.8rem;
    border-radius: 10px;
    background-color: var(--glass-bg) !important;
    color: var(--text-primary) !important;
    background-image: none !important;
}

/* Enhanced focus states for small selects */
.form-select-sm:focus {
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.15), 0 4px 15px rgba(0, 0, 0, 0.2);
    transform: translateY(-1px);
}

/* Disabled state styling */
.form-select:disabled {
    background-color: var(--bg-tertiary) !important;
    color: var(--text-muted) !important;
    border-color: var(--border-color) !important;
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: none !important;
}

/* Form Labels */
.form-label {
    color: var(--text-primary) !important;
    font-weight: 600;
    margin-bottom: 8px;
}

/* Enhanced Input Group Styling */
.input-group-text {
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    color: var(--text-primary) !important;
    font-weight: 600;
    border-radius: 12px 0 0 12px;
}

.input-group .form-control {
    border-radius: 0 12px 12px 0;
    border-left: none;
    color: var(--text-primary) !important;
}

.input-group .form-control:focus {
    border-left: 1px solid var(--accent-primary);
    color: var(--text-primary) !important;
}

/* Card Headers and Text */
.card-header h5 {
    color: var(--text-primary) !important;
    font-weight: 600;
}

.card-body {
    color: var(--text-primary) !important;
}

/* Ensure all text in cards is white */
.card * {
    color: var(--text-primary) !important;
}

/* Override Bootstrap's default text colors */
.text-muted {
    color: var(--text-muted) !important;
}

.text-secondary {
    color: var(--text-secondary) !important;
}

/* Gamma Regime Indicator Styling */
.gamma-regime-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 40px;
    width: 100%;
}

/* Gamma Regime Column Styling */
.gamma-regime-indicator .form-label {
    text-align: center !important;
    width: 100% !important;
    margin-bottom: 8px !important;
}

.gamma-regime-badge {
    font-size: 0.875rem !important;
    font-weight: 600 !important;
    padding: 10px 20px !important;
    border-radius: 8px !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
    transition: all var(--transition-normal) !important;
    border: 1px solid transparent !important;
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
    box-shadow: var(--shadow-md) !important;
    width: 100% !important;
    text-align: center !important;
    display: block !important;
    background: #000000 !important;
    color: var(--text-muted) !important;
}

/* Deep Positive Gamma - Black background with bright green text */
.gamma-regime-badge.gamma-regime-deep-positive {
    background: #000000 !important;
    color: #00FF00 !important;
    border-color: #00FF00 !important;
    box-shadow: 0 0 15px rgba(0, 255, 0, 0.2) !important;
}

/* Positive Gamma - Black background with green text */
.gamma-regime-badge.gamma-regime-positive {
    background: #000000 !important;
    color: #00CC00 !important;
    border-color: #00CC00 !important;
    box-shadow: 0 0 10px rgba(0, 204, 0, 0.15) !important;
}

/* Negative Gamma - Black background with orange-red text */
.gamma-regime-badge.gamma-regime-negative {
    background: #000000 !important;
    color: #FF6600 !important;
    border-color: #FF6600 !important;
    box-shadow: 0 0 10px rgba(255, 102, 0, 0.15) !important;
}

/* Deep Negative Gamma - Black background with bright red text */
.gamma-regime-badge.gamma-regime-deep-negative {
    background: #000000 !important;
    color: #FF0000 !important;
    border-color: #FF0000 !important;
    box-shadow: 0 0 15px rgba(255, 0, 0, 0.2) !important;
}

/* Loading state */
.gamma-regime-badge.gamma-regime-loading {
    background: #000000 !important;
    color: var(--text-muted) !important;
    border-color: var(--glass-border) !important;
}

/* Hover effects */
.gamma-regime-badge:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
}

/* Enhanced Button Styling */
.btn {
    border-radius: 12px;
    font-weight: 600;
    transition: all var(--transition-normal);
    border: none;
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.875rem;
    padding: 12px 24px;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left var(--transition-fast);
}

.btn:hover::before {
    left: 100%;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-primary {
    background: linear-gradient(135deg, var(--accent-primary), var(--accent-primary-hover));
    color: #000000;
    box-shadow: var(--shadow-md);
}

.btn-success {
    background: linear-gradient(135deg, var(--accent-success), var(--accent-success-hover));
    color: #000000;
    box-shadow: var(--shadow-md);
}

.btn-outline-light {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    color: var(--text-secondary);
    backdrop-filter: blur(10px);
}

.btn-outline-light:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: var(--border-hover);
    color: var(--text-primary);
    transform: translateY(-2px);
}

.btn-outline-warning {
    background: var(--glass-bg);
    border: 1px solid var(--accent-warning);
    color: var(--accent-warning);
    backdrop-filter: blur(10px);
}

.btn-outline-warning:hover {
    background: var(--accent-warning);
    color: var(--bg-primary);
}

.btn-outline-danger {
    background: var(--glass-bg);
    border: 1px solid #dc3545;
    color: #dc3545;
    backdrop-filter: blur(10px);
}

.btn-outline-danger:hover {
    background: #dc3545;
    border-color: #dc3545;
    color: #ffffff;
    transform: translateY(-2px);
}

/* Disconnect button styling */
#disconnect-data-btn {
    white-space: nowrap; /* Prevent text wrapping */
    min-width: fit-content; /* Ensure button doesn't shrink too much */
}

/* Responsive adjustments for disconnect button */
@media (max-width: 768px) {
    #disconnect-data-btn {
        font-size: 0.75rem;
        padding: 6px 12px;
    }

    #disconnect-data-btn .fas {
        font-size: 0.7rem;
    }

    /* Adjust navbar layout for tablets */
    .navbar .container-fluid {
        flex-wrap: wrap;
    }

    .navbar .navbar-text {
        font-size: 0.8rem;
    }
}

@media (max-width: 576px) {
    #disconnect-data-btn {
        font-size: 0;
        padding: 5px 8px;
    }

    #disconnect-data-btn .fas {
        font-size: 0.8rem;
    }

    /* Stack navbar elements on small screens */
    .navbar .container-fluid {
        flex-direction: column;
        gap: 0.5rem;
        padding: 0.5rem 1rem;
    }

    .navbar .d-flex:not(.navbar-brand) {
        width: 100%;
        justify-content: center;
    }

    .navbar-text {
        font-size: 0.75rem;
        text-align: center;
    }
}

/* Table styling */
.table-dark {
    --bs-table-bg: var(--bg-secondary);
    --bs-table-striped-bg: var(--bg-tertiary);
    --bs-table-hover-bg: rgba(255, 255, 255, 0.075);
}

.table-primary {
    --bs-table-bg: var(--accent-primary);
    --bs-table-color: white;
}

/* Enhanced Chart Container Styling */
.plotly-chart {
    background: var(--bg-chart);
    border-radius: 16px;
    padding: 20px;
    border: 1px solid var(--glass-border);
    box-shadow: inset 0 2px 10px rgba(0, 0, 0, 0.3);
}

/* Chart-specific styling */
.chart-container {
    background: var(--bg-chart);
    border-radius: 16px;
    padding: 20px;
    margin-bottom: 24px;
    border: 1px solid var(--glass-border);
    box-shadow: inset 0 2px 10px rgba(0, 0, 0, 0.3);
    position: relative;
}

.chart-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
}

/* Chart divs with enhanced styling */
#candlestick-chart,
#gex-chart,
#vgex-chart,
#dex-chart,
#dgex-chart,
#tex-chart,
#vegx-chart,
#vex-chart,
#cex-chart,
#oi-chart {
    background: var(--bg-chart);
    border-radius: 16px;
    border: 1px solid var(--glass-border);
    box-shadow: inset 0 2px 10px rgba(0, 0, 0, 0.3);
    position: relative;
    overflow: hidden;
}

#candlestick-chart::before,
#gex-chart::before,
#vgex-chart::before,
#dex-chart::before,
#dgex-chart::before,
#tex-chart::before,
#vegx-chart::before,
#vex-chart::before,
#cex-chart::before,
#oi-chart::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    z-index: 1;
}

/* Zone fill styling for charts */
.zone-fill-1 {
    opacity: var(--zone-alpha-1) !important;
}

.zone-fill-2 {
    opacity: var(--zone-alpha-2) !important;
}

.zone-fill-3 {
    opacity: var(--zone-alpha-3) !important;
}

/* Grid styling for charts */
.chart-grid {
    stroke: var(--grid-color);
    stroke-opacity: 0.6;
    stroke-width: 0.4;
}

/* Crosshair styling */
.crosshair-default {
    stroke: gray;
    stroke-width: 0.5;
    stroke-dasharray: 5,5;
}

.crosshair-trace {
    stroke: white;
    stroke-width: 1.0;
    stroke-dasharray: 3,3;
}

/* Enhanced Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.9), rgba(26, 26, 26, 0.95));
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    z-index: 9999;
    display: flex;
    justify-content: center;
    align-items: center;
    animation: fadeInUp 0.3s ease-out;
}

.loading-content {
    text-align: center;
    color: var(--text-primary);
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: 20px;
    padding: 40px;
    box-shadow: var(--shadow-xl);
}

.loading-content .spinner-border {
    width: 3rem;
    height: 3rem;
    border-width: 0.3em;
    border-color: var(--accent-primary);
    border-right-color: transparent;
    margin-bottom: 20px;
}

.loading-content h4 {
    color: var(--text-primary);
    font-weight: 600;
    margin-bottom: 10px;
}

.loading-content p {
    color: var(--text-secondary);
    margin: 0;
}

/* Modal styling */
.modal-content {
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
}

.modal-header {
    border-bottom: 1px solid var(--border-color);
}

.modal-footer {
    border-top: 1px solid var(--border-color);
}

/* Enhanced Alert Styling */
.alert {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: 16px;
    padding: 20px;
    margin-bottom: 24px;
    position: relative;
    overflow: hidden;
}

.alert::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--accent-primary), var(--accent-info));
}

.alert-info {
    border-left: 4px solid var(--accent-info);
    color: var(--accent-info);
}

.alert-info::before {
    background: linear-gradient(90deg, var(--accent-info), var(--accent-primary));
}

.alert-warning {
    border-left: 4px solid var(--accent-warning);
    color: var(--accent-warning);
}

.alert-warning::before {
    background: linear-gradient(90deg, var(--accent-warning), var(--accent-danger));
}

.alert strong {
    color: var(--text-primary);
    font-weight: 700;
}

/* Switch styling */
.form-check-input:checked {
    background-color: var(--accent-success);
    border-color: var(--accent-success);
}

/* Enhanced Navigation Bar */
.navbar {
    background: linear-gradient(135deg, #000000 0%, #1a1a1a 50%, #333333 100%) !important;
    border-bottom: 1px solid var(--glass-border);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    padding: 0.75rem 0;
    min-height: 70px;
}

.navbar .container-fluid {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
    background: linear-gradient(135deg, var(--accent-primary), var(--accent-info));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin: 0;
    padding: 0;
}

.navbar-brand i {
    font-size: 1.8rem;
    background: linear-gradient(135deg, var(--accent-primary), var(--accent-info));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.navbar-nav {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin: 0;
}

.navbar-text {
    color: var(--text-secondary) !important;
    font-weight: 500;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* Enhanced Connection Status */
.connection-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    border-radius: 25px;
    padding: 8px 16px;
    font-size: 0.875rem;
    font-weight: 600;
    transition: all var(--transition-normal);
}

.connection-indicator:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
}

.connection-indicator i {
    font-size: 0.75rem;
    animation: pulse 2s infinite;
}

#connection-status {
    font-weight: 600;
}

.status-connected {
    color: var(--accent-success);
}

.status-connected i {
    color: var(--accent-success);
    text-shadow: 0 0 8px rgba(16, 185, 129, 0.6);
}

.status-disconnected {
    color: var(--accent-danger);
}

.status-disconnected i {
    color: var(--accent-danger);
    text-shadow: 0 0 8px rgba(239, 68, 68, 0.6);
}

.status-loading {
    color: var(--accent-warning);
}

.status-loading i {
    color: var(--accent-warning);
    text-shadow: 0 0 8px rgba(245, 158, 11, 0.6);
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Navigation Buttons */
.navbar .btn {
    padding: 10px 20px;
    font-size: 0.875rem;
    font-weight: 600;
    border-radius: 25px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all var(--transition-normal);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    min-width: 120px;
    justify-content: center;
}

.navbar .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.navbar .btn i {
    font-size: 0.875rem;
}

/* Badge Styling */
.badge {
    background: linear-gradient(135deg, var(--accent-info), var(--accent-primary)) !important;
    color: white !important;
    font-weight: 600;
    padding: 4px 10px;
    border-radius: 15px;
    font-size: 0.7rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-left: 0.5rem;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}



/* Enhanced Responsive Design */
@media (max-width: 1200px) {
    .container-fluid {
        max-width: 100%;
        padding: 0 15px;
    }

    .nav-tabs .nav-link {
        padding: 10px 16px;
        font-size: 0.8rem;
    }
}

@media (max-width: 768px) {
    .container-fluid {
        padding: 0 10px;
    }

    .card {
        border-radius: 12px;
        margin-bottom: 16px;
    }

    .card-body {
        padding: 16px;
    }

    .nav-tabs .nav-link {
        font-size: 0.75rem;
        padding: 8px 12px;
        margin-right: 2px;
    }

    .chart-settings-panel {
        padding: 16px;
        margin-bottom: 16px;
    }

    .btn {
        padding: 10px 20px;
        font-size: 0.8rem;
    }



    .form-control, .form-select {
        padding: 12px 14px;
        font-size: 0.875rem;
        border-radius: 10px;
    }

    .form-select-sm {
        padding: 8px 12px;
        font-size: 0.8rem;
    }
}

/* Chart Wrapper and Fullscreen Styles */
.chart-wrapper {
    position: relative;
}

.chart-fullscreen-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 10;
    background: rgba(0, 0, 0, 0.7);
    border: 1px solid var(--glass-border);
    backdrop-filter: blur(10px);
    transition: all var(--transition-fast);
}

.chart-fullscreen-btn:hover {
    background: var(--accent-primary);
    border-color: var(--accent-primary);
    transform: scale(1.05);
}

/* Fullscreen Modal */
.chart-fullscreen-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: var(--bg-primary);
    z-index: 9999;
    overflow: hidden;
}

.chart-fullscreen-modal.active {
    display: flex;
    flex-direction: column;
}

.chart-fullscreen-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--glass-border);
    box-shadow: var(--shadow-md);
}

.chart-fullscreen-header h5 {
    margin: 0;
    color: var(--text-primary);
    font-weight: 600;
}

.chart-fullscreen-content {
    flex: 1;
    padding: 20px;
    overflow: hidden;
}

#fullscreen-chart-container {
    width: 100%;
    height: 100%;
    background: var(--bg-chart);
    border-radius: 16px;
    border: 1px solid var(--glass-border);
    box-shadow: inset 0 2px 10px rgba(0, 0, 0, 0.3);
}

/* Fullscreen chart specific styling */
.chart-fullscreen-modal .plotly-chart,
.chart-fullscreen-modal #fullscreen-chart-container > div {
    width: 100% !important;
    height: 100% !important;
}

/* Animation for fullscreen transition */
.chart-fullscreen-modal {
    animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: scale(0.95);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@media (max-width: 576px) {
    .navbar {
        padding: 0.5rem 0;
        min-height: 60px;
    }

    .navbar-brand {
        font-size: 1.1rem;
    }

    .navbar-brand i {
        font-size: 1.3rem;
    }

    .badge {
        font-size: 0.6rem;
        padding: 2px 6px;
    }

    .connection-indicator {
        padding: 6px 12px;
        font-size: 0.75rem;
    }

    .navbar .btn {
        padding: 8px 16px;
        font-size: 0.75rem;
        min-width: 100px;
    }

    .nav-tabs .nav-link {
        font-size: 0.7rem;
        padding: 6px 8px;
        margin-right: 1px;
    }

    .card {
        border-radius: 10px;
    }

    .chart-settings-panel {
        padding: 12px;
    }
}

/* Additional navbar improvements */
@media (max-width: 768px) {
    .navbar .container-fluid {
        flex-wrap: nowrap;
    }

    .navbar-nav {
        gap: 0.5rem;
    }

    .connection-indicator {
        padding: 6px 10px;
        font-size: 0.8rem;
    }

    .navbar .btn {
        padding: 8px 14px;
        font-size: 0.8rem;
        min-width: 110px;
    }
}

/* Enhanced Scrollbar Styling */
::-webkit-scrollbar {
    width: 12px;
    height: 12px;
}

::-webkit-scrollbar-track {
    background: var(--bg-secondary);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, var(--accent-primary), var(--accent-info));
    border-radius: 10px;
    border: 2px solid var(--bg-secondary);
    transition: all var(--transition-normal);
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, var(--accent-primary-hover), var(--accent-info-hover));
    transform: scale(1.1);
}

::-webkit-scrollbar-corner {
    background: var(--bg-secondary);
}

/* Enhanced Animations */
@keyframes pulse {
    0% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.7;
        transform: scale(1.02);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes shimmer {
    0% {
        background-position: -200px 0;
    }
    100% {
        background-position: calc(200px + 100%) 0;
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes glow {
    0%, 100% {
        box-shadow: 0 0 5px rgba(59, 130, 246, 0.5);
    }
    50% {
        box-shadow: 0 0 20px rgba(59, 130, 246, 0.8);
    }
}

.loading-pulse {
    animation: pulse 1.5s ease-in-out infinite;
}

.shimmer {
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite;
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

.slide-in-right {
    animation: slideInRight 0.4s ease-out;
}

.glow-effect {
    animation: glow 2s ease-in-out infinite;
}

/* Chart legend styling */
.plotly .legend {
    background-color: rgba(45, 45, 45, 0.9) !important;
    border: 1px solid var(--border-color) !important;
}

/* Tooltip styling */
.tooltip-inner {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
}

.tooltip .tooltip-arrow {
    border-top-color: var(--bg-tertiary);
}

/* Status indicators */
.status-connected {
    color: var(--accent-success);
}

.status-disconnected {
    color: var(--accent-danger);
}

.status-loading {
    color: var(--accent-warning);
}

/* Tab Styling - Matching Desktop Dashboard Groups */
.nav-tabs {
    border-bottom: 1px solid var(--border-color);
}

.nav-tabs .nav-link {
    background-color: var(--bg-tertiary);
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
    border-bottom: none;
    margin-right: 2px;
    font-size: 11px;
    font-weight: bold;
    padding: 6px 12px;
}

.nav-tabs .nav-link:hover {
    background-color: var(--bg-secondary);
    color: var(--text-primary);
}

.nav-tabs .nav-link.active {
    background-color: var(--bg-chart);
    color: var(--text-primary);
    border-bottom: 1px solid var(--bg-chart);
}

/* Tab Group 1: Candlestick Chart */
.nav-tabs .nav-link[data-group="candlestick"] {
    margin-right: 12px;
    border-right: 2px solid var(--grid-color);
    box-shadow: none;
}

/* Tab Group 2: Gamma-related exposure metrics */
.nav-tabs .nav-link[data-group="gamma"] {
    box-shadow: none;
}

/* Tab Group 3: Basic exposure metrics */
.nav-tabs .nav-link[data-group="basic"] {
    box-shadow: none;
}

/* Tab Group 4: Advanced exposure metrics */
.nav-tabs .nav-link[data-group="advanced"] {
    box-shadow: none;
}

/* Tab Group 5: Open Interest */
.nav-tabs .nav-link[data-group="oi"] {
    box-shadow: none;
}

/* Tab Group 6: GEX Visualizer */
.nav-tabs .nav-link[data-group="visualizer"] {
    margin-left: 12px;
    border-left: 2px solid var(--grid-color);
    box-shadow: none;
}

/* Tab Group 7: Volatility */
.nav-tabs .nav-link[data-group="volatility"] {
    margin-left: 12px;
    border-left: 2px solid var(--grid-color);
    box-shadow: none;
}

/* Tab Group 8: Positioning */
.nav-tabs .nav-link[data-group="positioning"] {
    margin-left: 12px;
    border-left: 2px solid var(--grid-color);
    box-shadow: none;
}

/* Active tab styling for all groups */
.nav-tabs .nav-link.active {
    background-color: var(--bg-chart) !important;
    border-bottom: 1px solid var(--bg-chart) !important;
}

/* Price display styling */
.price-positive {
    color: var(--accent-success);
}

.price-negative {
    color: var(--accent-danger);
}

.price-neutral {
    color: var(--text-secondary);
}

/* Market state indicators - removed as current price display was removed */

/* Enhanced Chart Settings Panel */
.chart-settings-panel {
    background: var(--bg-tertiary);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: 16px;
    padding: 24px;
    margin-bottom: 24px;
    position: relative;
    overflow: hidden;
}

.chart-settings-panel::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
}

.chart-settings-panel .form-label {
    color: var(--text-secondary);
    font-size: 0.875rem;
    font-weight: 600;
    margin-bottom: 8px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.chart-settings-panel .form-select,
.chart-settings-panel .form-control {
    background: rgba(255, 255, 255, 0.06) !important;
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    border: 1px solid var(--glass-border);
    border-radius: 12px;
    color: var(--text-primary) !important;
    font-size: 0.875rem;
    font-weight: 500;
    padding: 12px 16px;
    transition: all var(--transition-normal);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.chart-settings-panel .form-select {
    cursor: pointer;
    background-image: none !important;
}

/* Chart settings panel dropdown options */
.chart-settings-panel .form-select option {
    background-color: var(--bg-secondary) !important;
    color: var(--text-primary) !important;
    padding: 12px 16px !important;
    border: none !important;
    font-weight: 500;
}

.chart-settings-panel .form-select option:hover {
    background-color: var(--bg-tertiary) !important;
    color: var(--text-primary) !important;
}

.chart-settings-panel .form-select option:checked,
.chart-settings-panel .form-select option:selected {
    background-color: var(--accent-primary) !important;
    color: #000000 !important;
    font-weight: 600;
}

.chart-settings-panel .form-select:focus,
.chart-settings-panel .form-control:focus {
    background: rgba(255, 255, 255, 0.1);
    border-color: var(--accent-primary);
    color: var(--text-primary);
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2), 0 6px 20px rgba(0, 0, 0, 0.2);
    transform: translateY(-1px);
}



.chart-settings-panel .form-select:hover,
.chart-settings-panel .form-control:hover {
    background: rgba(255, 255, 255, 0.08);
    border-color: var(--border-hover);
    transform: translateY(-0.5px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

.chart-settings-panel .form-check-label {
    color: var(--text-secondary);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: color var(--transition-fast);
}

.chart-settings-panel .form-check-label:hover {
    color: var(--text-primary);
}

.chart-settings-panel .form-check-input {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: 6px;
}

.chart-settings-panel .form-check-input:checked {
    background: var(--accent-primary);
    border-color: var(--accent-primary);
}

/* Additional Modern Enhancements */
.badge {
    background: linear-gradient(135deg, var(--accent-primary), var(--accent-info)) !important;
    color: white !important;
    font-weight: 600;
    padding: 6px 12px;
    border-radius: 20px;
}

/* Color Legend Badges - Specific classes to override general badge styling */
.d-flex .color-legend-badge.color-legend-badge {
    font-weight: 600 !important;
    padding: 4px 8px !important;
    border-radius: 4px !important;
    color: white !important;
    font-size: 0.75rem !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    background: none !important; /* Override any gradient backgrounds */
    background-image: none !important; /* Override any gradient backgrounds */
    box-shadow: none !important; /* Remove any box shadows */
    text-transform: none !important; /* Remove text transform */
    letter-spacing: normal !important; /* Reset letter spacing */
    margin-left: 0 !important; /* Reset margin */
    margin-right: 2px !important; /* Add small gap between badges */
}

.d-flex .color-legend-badge.negative-3.color-legend-badge {
    background-color: #991b1b !important;
    background: #991b1b !important; /* Ensure solid color */
    background-image: none !important; /* Override any gradient backgrounds */
}

.d-flex .color-legend-badge.negative-2.color-legend-badge {
    background-color: #b91c1c !important;
    background: #b91c1c !important; /* Ensure solid color */
    background-image: none !important; /* Override any gradient backgrounds */
}

.d-flex .color-legend-badge.negative-1.color-legend-badge {
    background-color: #dc2626 !important;
    background: #dc2626 !important; /* Ensure solid color */
    background-image: none !important; /* Override any gradient backgrounds */
}

.d-flex .color-legend-badge.neutral.color-legend-badge {
    background-color: #6b7280 !important;
    background: #6b7280 !important; /* Ensure solid color */
    background-image: none !important; /* Override any gradient backgrounds */
}

.d-flex .color-legend-badge.positive-1.color-legend-badge {
    background-color: #15803d !important;
    background: #15803d !important; /* Ensure solid color */
    background-image: none !important; /* Override any gradient backgrounds */
}

.d-flex .color-legend-badge.positive-2.color-legend-badge {
    background-color: #16a34a !important;
    background: #16a34a !important; /* Ensure solid color */
    background-image: none !important; /* Override any gradient backgrounds */
}

.d-flex .color-legend-badge.positive-3.color-legend-badge {
    background-color: #22c55e !important;
    background: #22c55e !important; /* Ensure solid color */
    background-image: none !important; /* Override any gradient backgrounds */
}

/* Sector Color Key - Simple approach with inline styles */
.sector-color-key {
    display: inline-block !important;
    margin-right: 2px !important;
}
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Enhanced Table Styling */
.table-dark {
    --bs-table-bg: var(--glass-bg);
    --bs-table-striped-bg: rgba(255, 255, 255, 0.02);
    --bs-table-hover-bg: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    overflow: hidden;
}

.table-dark th {
    background: var(--glass-bg);
    border-color: var(--glass-border);
    color: var(--text-primary);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.875rem;
}

.table-dark td {
    border-color: var(--glass-border);
    color: var(--text-secondary);
}

/* Enhanced Modal Styling */
.modal-content {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: 16px;
    box-shadow: var(--shadow-xl);
}

.modal-header {
    border-bottom: 1px solid var(--divider-color);
    background: rgba(255, 255, 255, 0.03);
}

.modal-footer {
    border-top: 1px solid var(--divider-color);
    background: rgba(255, 255, 255, 0.03);
}

/* Tooltip Enhancements */
.tooltip-inner {
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    color: var(--text-primary);
    border-radius: 8px;
    padding: 8px 12px;
    font-size: 0.875rem;
}

/* Focus States */
*:focus {
    outline: none;
}

.form-control:focus,
.form-select:focus,
.btn:focus {
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.15) !important;
}

/* Advanced Dropdown Styling */
.form-select-enhanced {
    position: relative;
    overflow: hidden;
}

.form-select-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left var(--transition-fast);
    pointer-events: none;
}

.form-select-enhanced:hover::before {
    left: 100%;
}

/* Dropdown Loading State */
.form-select.loading {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Dropdown with Icons */
.form-select-with-icon {
    padding-left: 45px;
}

.form-select-with-icon.form-select-sm {
    padding-left: 38px;
}

/* Enhanced Error States */
.form-select.is-invalid {
    border-color: var(--accent-danger);
    animation: shake 0.5s ease-in-out;
}

.form-select.is-valid {
    border-color: var(--accent-success);
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* Utility Classes */
.text-gradient {
    background: linear-gradient(135deg, var(--accent-primary), var(--accent-info));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.glass-panel {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: 16px;
}

.hover-lift {
    transition: transform var(--transition-normal);
}

.hover-lift:hover {
    transform: translateY(-4px);
}

/* Specific fixes for trading controls text color */
.card .form-label,
.card label,
.card .form-check-label,
.card .btn,
.card .input-group-text,
.card .form-control,
.card .form-select,
.card h1, .card h2, .card h3, .card h4, .card h5, .card h6,
.card p, .card span, .card div {
    color: var(--text-primary) !important;
}

/* Bootstrap overrides for dark theme */
.form-control:disabled,
.form-control[readonly] {
    background-color: var(--bg-tertiary) !important;
    color: var(--text-secondary) !important;
    opacity: 0.8;
}

/* Small text elements */
.small, small {
    color: var(--text-muted) !important;
}

/* Form check elements */
.form-check-input {
    background-color: var(--glass-bg) !important;
    border-color: var(--glass-border) !important;
}

/* Enhanced Dropdown Menu Styling for Better UX */
.form-select optgroup {
    background-color: var(--bg-tertiary) !important;
    color: var(--text-secondary) !important;
    font-weight: 600;
    font-size: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    padding: 8px 16px !important;
}

/* Custom scrollbar for dropdown options */
.form-select::-webkit-scrollbar {
    width: 8px;
}

.form-select::-webkit-scrollbar-track {
    background: var(--bg-secondary);
    border-radius: 4px;
}

.form-select::-webkit-scrollbar-thumb {
    background: var(--glass-border);
    border-radius: 4px;
    transition: background var(--transition-fast);
}

.form-select::-webkit-scrollbar-thumb:hover {
    background: var(--border-hover);
}

/* Dropdown focus ring enhancement */
.form-select:focus-visible {
    outline: 2px solid var(--accent-primary);
    outline-offset: 2px;
}

/* Better contrast for selected options */
.form-select option:checked,
.form-select option[selected] {
    background: var(--accent-primary) !important;
    color: #000000 !important;
    font-weight: 600;
}

/* Force dropdown styling across all browsers */
select.form-select {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background-color: var(--glass-bg) !important;
    color: var(--text-primary) !important;
}

/* Firefox specific dropdown styling */
@-moz-document url-prefix() {
    .form-select {
        background-color: var(--glass-bg) !important;
        color: var(--text-primary) !important;
    }

    .form-select option {
        background-color: var(--bg-secondary) !important;
        color: var(--text-primary) !important;
    }
}

/* WebKit specific dropdown styling */
@media screen and (-webkit-min-device-pixel-ratio:0) {
    .form-select {
        background-color: var(--glass-bg) !important;
        color: var(--text-primary) !important;
    }
}

/* Improved loading state */
.form-select[data-loading="true"] {
    animation: spin 1s linear infinite;
    pointer-events: none;
}

.form-check-input:checked {
    background-color: var(--accent-primary) !important;
    border-color: var(--accent-primary) !important;
}

.form-check-label {
    color: var(--text-primary) !important;
    cursor: pointer;
}

/* Switch styling */
.form-switch .form-check-input {
    background-color: var(--bg-tertiary) !important;
    border-color: var(--border-color) !important;
}

.form-switch .form-check-input:checked {
    background-color: var(--accent-success) !important;
    border-color: var(--accent-success) !important;
}

.chart-settings-panel .form-check-input:checked {
    background-color: var(--accent-primary);
    border-color: var(--accent-primary);
}

.chart-settings-panel .form-check-input:focus {
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.chart-settings-panel .row {
    align-items: end;
}

/* Chart Help Button Styling */
.chart-help-btn {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    color: var(--text-secondary);
    backdrop-filter: blur(10px);
    border-radius: 8px;
    padding: 6px 10px;
    font-size: 0.875rem;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.chart-help-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: var(--border-hover);
    color: var(--text-primary);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.chart-help-btn:focus {
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
    outline: none;
}

.chart-help-btn i {
    font-size: 1rem;
}

/* Chart Settings Panel Header */
.chart-settings-panel .d-flex {
    border-bottom: 1px solid var(--divider-color);
    padding-bottom: 0.5rem;
    margin-bottom: 1rem;
}

.chart-settings-panel h6 {
    color: var(--text-primary);
    font-weight: 600;
    font-size: 0.95rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Help Modal Styling */
#chart-help-modal .modal-content {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: 16px;
    box-shadow: var(--glass-shadow);
}

#chart-help-modal .modal-header {
    background: rgba(255, 255, 255, 0.03);
    border-bottom: 1px solid var(--divider-color);
    border-radius: 16px 16px 0 0;
}

#chart-help-modal .modal-body {
    background: transparent;
    color: var(--text-primary);
}

#chart-help-modal .modal-body h6 {
    color: var(--text-primary);
    font-weight: 600;
    margin-top: 1.5rem;
    margin-bottom: 0.75rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--divider-color);
}

#chart-help-modal .modal-body h6:first-child {
    margin-top: 0;
}

#chart-help-modal .modal-body ul {
    margin-bottom: 1rem;
}

#chart-help-modal .modal-body li {
    margin-bottom: 0.5rem;
    line-height: 1.6;
}

#chart-help-modal .modal-body strong {
    color: var(--accent-primary);
    font-weight: 600;
}

#chart-help-modal .modal-footer {
    background: rgba(255, 255, 255, 0.03);
    border-top: 1px solid var(--divider-color);
    border-radius: 0 0 16px 16px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container-fluid {
        padding: 0 10px;
    }

    .card {
        margin-bottom: 1rem;
        border-radius: 12px;
    }

    .nav-tabs .nav-link {
        padding: 8px 12px;
        font-size: 0.75rem;
    }

    .btn {
        padding: 8px 16px;
        font-size: 0.75rem;
    }

    .form-control, .form-select {
        padding: 10px 14px;
        font-size: 0.8rem;
    }

    .chart-wrapper {
        margin-bottom: 1rem;
    }

    .chart-settings-panel {
        padding: 1rem;
    }

    .chart-settings-panel .row {
        gap: 0.5rem;
    }

    .chart-settings-panel .col-md-2,
    .chart-settings-panel .col-md-3 {
        flex: 0 0 100%;
        max-width: 100%;
        margin-bottom: 0.5rem;
    }

    .chart-help-btn {
        padding: 4px 8px;
        font-size: 0.8rem;
    }

    .chart-settings-panel h6 {
        font-size: 0.85rem;
    }
}
